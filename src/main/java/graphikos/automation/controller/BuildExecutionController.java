package graphikos.automation.controller;

import graphikos.automation.model.BuildExecution;
import graphikos.automation.model.BuildRequest;
import graphikos.automation.service.BuildExecutionService;
import graphikos.automation.service.BuildService;
import graphikos.automation.service.ExternalBuildSystemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/build-executions")
public class BuildExecutionController {

    @Autowired
    private BuildExecutionService buildExecutionService;

    @Autowired
    private BuildService buildService;

    @Autowired
    private ExternalBuildSystemService externalBuildSystemService;

    /**
     * Get all executions for a build request
     */
    @GetMapping("/build/{buildRequestId}")
    public ResponseEntity<Map<String, Object>> getExecutions(@PathVariable Long buildRequestId) {
        try {
            Optional<BuildRequest> buildRequestOpt = buildService.findById(buildRequestId);
            if (buildRequestOpt.isPresent()) {
                BuildRequest buildRequest = buildRequestOpt.get();
                
                List<BuildExecution> allExecutions = buildExecutionService.getExecutionsByBuildRequest(buildRequest);
                List<BuildExecution> latestExecutions = buildExecutionService.getLatestExecutions(buildRequest);
                Map<BuildExecution.BuildSystem, List<BuildExecution>> summary = 
                    buildExecutionService.getExecutionSummary(buildRequest);
                
                Map<String, Object> response = new HashMap<>();
                response.put("success", true);
                response.put("allExecutions", convertExecutionsToMap(allExecutions));
                response.put("latestExecutions", convertExecutionsToMap(latestExecutions));
                response.put("summary", convertSummaryToMap(summary));
                response.put("allBuildsSuccessful", buildExecutionService.areAllBuildsSuccessful(buildRequest));
                response.put("buildRequestStatus", buildRequest.getStatus().name());
                
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Error fetching executions: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Manually trigger a build execution (for testing)
     */
    @PostMapping("/{executionId}/trigger")
    public ResponseEntity<Map<String, Object>> triggerBuild(@PathVariable Long executionId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            Optional<BuildExecution> executionOpt = buildExecutionService.findById(executionId);
            if (executionOpt.isPresent()) {
                BuildExecution execution = executionOpt.get();
                
                // Trigger build in external system (simulated)
                externalBuildSystemService.triggerBuild(
                    executionId, 
                    execution.getProductName(), 
                    execution.getBuildSystem()
                );
                
                response.put("success", true);
                response.put("message", "Build triggered successfully");
                response.put("execution", convertExecutionToMap(execution));
                
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "Build execution not found");
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Error triggering build: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Retry a failed execution
     */
    @PostMapping("/{executionId}/retry")
    public ResponseEntity<Map<String, Object>> retryExecution(@PathVariable Long executionId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            BuildExecution newExecution = buildExecutionService.retryExecution(executionId);
            
            response.put("success", true);
            response.put("message", "Build execution retry created successfully");
            response.put("execution", convertExecutionToMap(newExecution));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Error retrying execution: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Update execution status (for external system callbacks)
     */
    @PutMapping("/{executionId}/status")
    public ResponseEntity<Map<String, Object>> updateExecutionStatus(
            @PathVariable Long executionId,
            @RequestBody Map<String, Object> request) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            String statusStr = (String) request.get("status");
            String errorMessage = (String) request.get("errorMessage");
            
            BuildExecution.ExecutionStatus status = BuildExecution.ExecutionStatus.valueOf(statusStr.toUpperCase());
            BuildExecution execution = buildExecutionService.updateExecutionStatus(executionId, status, errorMessage);
            
            response.put("success", true);
            response.put("message", "Execution status updated successfully");
            response.put("execution", convertExecutionToMap(execution));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Error updating execution status: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Get failed executions that can be retried
     */
    @GetMapping("/build/{buildRequestId}/failed")
    public ResponseEntity<List<Map<String, Object>>> getFailedExecutions(@PathVariable Long buildRequestId) {
        try {
            Optional<BuildRequest> buildRequestOpt = buildService.findById(buildRequestId);
            if (buildRequestOpt.isPresent()) {
                BuildRequest buildRequest = buildRequestOpt.get();
                List<BuildExecution> failedExecutions = buildExecutionService.getFailedExecutionsForRetry(buildRequest);
                
                return ResponseEntity.ok(convertExecutionsToMap(failedExecutions));
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    // Helper methods to convert entities to maps for JSON response
    private List<Map<String, Object>> convertExecutionsToMap(List<BuildExecution> executions) {
        return executions.stream()
                .map(this::convertExecutionToMap)
                .collect(Collectors.toList());
    }

    private Map<String, Object> convertExecutionToMap(BuildExecution execution) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", execution.getId());
        map.put("productName", execution.getProductName());
        map.put("buildSystem", execution.getBuildSystem().name());
        map.put("status", execution.getStatus().name());
        map.put("startedTime", execution.getStartedTime() != null ? execution.getStartedTime().toString() : null);
        map.put("completedTime", execution.getCompletedTime() != null ? execution.getCompletedTime().toString() : null);
        map.put("buildId", execution.getBuildId());
        map.put("buildUrl", execution.getBuildUrl());
        map.put("errorMessage", execution.getErrorMessage());
        map.put("retryCount", execution.getRetryCount());
        map.put("durationMinutes", execution.getDurationInMinutes());
        map.put("createdAt", execution.getCreatedAt().toString());
        return map;
    }

    private Map<String, Object> convertSummaryToMap(Map<BuildExecution.BuildSystem, List<BuildExecution>> summary) {
        Map<String, Object> result = new HashMap<>();
        for (Map.Entry<BuildExecution.BuildSystem, List<BuildExecution>> entry : summary.entrySet()) {
            result.put(entry.getKey().name(), convertExecutionsToMap(entry.getValue()));
        }
        return result;
    }
}
