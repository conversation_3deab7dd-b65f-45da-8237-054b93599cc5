package graphikos.automation.model;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

@Entity
@Table(name = "build_executions")
public class BuildExecution {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "build_request_id", nullable = false)
    private BuildRequest buildRequest;

    @NotBlank(message = "Product name is required")
    private String productName;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private BuildSystem buildSystem;

    @Enumerated(EnumType.STRING)
    private ExecutionStatus status = ExecutionStatus.PENDING;

    private LocalDateTime startedTime;
    private LocalDateTime completedTime;
    private LocalDateTime createdAt = LocalDateTime.now();
    private LocalDateTime updatedAt = LocalDateTime.now();

    // Additional tracking fields
    private String buildId; // External build system ID
    private String buildUrl; // URL to external build system
    private String errorMessage; // Error details if failed
    private int retryCount = 0; // Number of retries for this product

    public enum BuildSystem {
        CMTOOLS, SERVICE_DELIVERY
    }

    public enum ExecutionStatus {
        PENDING, STARTED, SUCCESS, FAILED, CANCELLED
    }

    // Constructors
    public BuildExecution() {}

    public BuildExecution(BuildRequest buildRequest, String productName, BuildSystem buildSystem) {
        this.buildRequest = buildRequest;
        this.productName = productName;
        this.buildSystem = buildSystem;
        this.status = ExecutionStatus.PENDING;
    }

    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public BuildRequest getBuildRequest() { return buildRequest; }
    public void setBuildRequest(BuildRequest buildRequest) { this.buildRequest = buildRequest; }

    public String getProductName() { return productName; }
    public void setProductName(String productName) { this.productName = productName; }

    public BuildSystem getBuildSystem() { return buildSystem; }
    public void setBuildSystem(BuildSystem buildSystem) { this.buildSystem = buildSystem; }

    public ExecutionStatus getStatus() { return status; }
    public void setStatus(ExecutionStatus status) { this.status = status; }

    public LocalDateTime getStartedTime() { return startedTime; }
    public void setStartedTime(LocalDateTime startedTime) { this.startedTime = startedTime; }

    public LocalDateTime getCompletedTime() { return completedTime; }
    public void setCompletedTime(LocalDateTime completedTime) { this.completedTime = completedTime; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }

    public String getBuildId() { return buildId; }
    public void setBuildId(String buildId) { this.buildId = buildId; }

    public String getBuildUrl() { return buildUrl; }
    public void setBuildUrl(String buildUrl) { this.buildUrl = buildUrl; }

    public String getErrorMessage() { return errorMessage; }
    public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }

    public int getRetryCount() { return retryCount; }
    public void setRetryCount(int retryCount) { this.retryCount = retryCount; }

    // Helper methods
    public boolean isCompleted() {
        return status == ExecutionStatus.SUCCESS || status == ExecutionStatus.FAILED || status == ExecutionStatus.CANCELLED;
    }

    public boolean isSuccessful() {
        return status == ExecutionStatus.SUCCESS;
    }

    public boolean isFailed() {
        return status == ExecutionStatus.FAILED;
    }

    public long getDurationInMinutes() {
        if (startedTime != null && completedTime != null) {
            return java.time.Duration.between(startedTime, completedTime).toMinutes();
        }
        return 0;
    }
}
