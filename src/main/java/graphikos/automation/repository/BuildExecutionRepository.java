package graphikos.automation.repository;

import graphikos.automation.model.BuildExecution;
import graphikos.automation.model.BuildRequest;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface BuildExecutionRepository extends JpaRepository<BuildExecution, Long> {
    
    // Find all executions for a specific build request
    List<BuildExecution> findByBuildRequestOrderByCreatedAtDesc(BuildRequest buildRequest);
    
    // Find executions by build request and build system
    List<BuildExecution> findByBuildRequestAndBuildSystemOrderByCreatedAtDesc(
        BuildRequest buildRequest, BuildExecution.BuildSystem buildSystem);
    
    // Find executions by build request and product name
    List<BuildExecution> findByBuildRequestAndProductNameOrderByCreatedAtDesc(
        BuildRequest buildRequest, String productName);
    
    // Find the latest execution for each product in a build request
    @Query("SELECT be FROM BuildExecution be WHERE be.buildRequest = :buildRequest " +
           "AND be.createdAt = (SELECT MAX(be2.createdAt) FROM BuildExecution be2 " +
           "WHERE be2.buildRequest = :buildRequest AND be2.productName = be.productName)")
    List<BuildExecution> findLatestExecutionsByBuildRequest(@Param("buildRequest") BuildRequest buildRequest);
    
    // Check if all latest executions for a build request are successful
    @Query("SELECT COUNT(be) = 0 FROM BuildExecution be WHERE be.buildRequest = :buildRequest " +
           "AND be.createdAt = (SELECT MAX(be2.createdAt) FROM BuildExecution be2 " +
           "WHERE be2.buildRequest = :buildRequest AND be2.productName = be.productName) " +
           "AND be.status != 'SUCCESS'")
    boolean areAllLatestExecutionsSuccessful(@Param("buildRequest") BuildRequest buildRequest);
    
    // Count executions by status for a build request
    long countByBuildRequestAndStatus(BuildRequest buildRequest, BuildExecution.ExecutionStatus status);
    
    // Find executions that are currently running (STARTED status)
    List<BuildExecution> findByBuildRequestAndStatus(BuildRequest buildRequest, BuildExecution.ExecutionStatus status);
    
    // Find executions by external build ID
    Optional<BuildExecution> findByBuildId(String buildId);
    
    // Find failed executions that can be retried
    @Query("SELECT be FROM BuildExecution be WHERE be.buildRequest = :buildRequest " +
           "AND be.status = 'FAILED' " +
           "AND be.createdAt = (SELECT MAX(be2.createdAt) FROM BuildExecution be2 " +
           "WHERE be2.buildRequest = :buildRequest AND be2.productName = be.productName)")
    List<BuildExecution> findFailedExecutionsForRetry(@Param("buildRequest") BuildRequest buildRequest);
}
