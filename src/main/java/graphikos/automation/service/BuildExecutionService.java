package graphikos.automation.service;

import graphikos.automation.model.BuildExecution;
import graphikos.automation.model.BuildRequest;
import graphikos.automation.repository.BuildExecutionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class BuildExecutionService {

    @Autowired
    private BuildExecutionRepository buildExecutionRepository;

    @Autowired
    private BuildService buildService;

    /**
     * Initialize build executions for a build request based on the configured products
     */
    public void initializeBuildExecutions(BuildRequest buildRequest) {
        // Create executions for CMTools products
        createExecutionIfProductConfigured(buildRequest, "showpic", BuildExecution.BuildSystem.CMTOOLS);
        createExecutionIfProductConfigured(buildRequest, "show3", BuildExecution.BuildSystem.CMTOOLS);
        createExecutionIfProductConfigured(buildRequest, "showserver", BuildExecution.BuildSystem.CMTOOLS);
        
        // Create executions for Service Delivery products
        createExecutionIfProductConfigured(buildRequest, "show3", BuildExecution.BuildSystem.SERVICE_DELIVERY);
        createExecutionIfProductConfigured(buildRequest, "conversion", BuildExecution.BuildSystem.SERVICE_DELIVERY);
        createExecutionIfProductConfigured(buildRequest, "pictures", BuildExecution.BuildSystem.SERVICE_DELIVERY);
        createExecutionIfProductConfigured(buildRequest, "imageconversion", BuildExecution.BuildSystem.SERVICE_DELIVERY);

        // Update build request status
        buildRequest.setStatus(BuildRequest.BuildStatus.BUILD_TRACKING);
        buildService.updateBuildRequest(buildRequest);
    }

    private void createExecutionIfProductConfigured(BuildRequest buildRequest, String productName, 
                                                   BuildExecution.BuildSystem buildSystem) {
        // Check if the product is configured in the build request
        boolean isConfigured = false;
        
        switch (productName.toLowerCase()) {
            case "showpic":
                isConfigured = buildRequest.getGraphikosmedia() != null && !buildRequest.getGraphikosmedia().trim().isEmpty();
                break;
            case "show3":
                isConfigured = buildRequest.getShowui() != null && !buildRequest.getShowui().trim().isEmpty();
                break;
            case "showserver":
                isConfigured = buildRequest.getShowserver() != null && !buildRequest.getShowserver().trim().isEmpty();
                break;
            case "conversion":
                isConfigured = buildRequest.getConversion() != null && !buildRequest.getConversion().trim().isEmpty();
                break;
            case "pictures":
                isConfigured = buildRequest.getPictures() != null && !buildRequest.getPictures().trim().isEmpty();
                break;
            case "imageconversion":
                isConfigured = buildRequest.getImageconversion() != null && !buildRequest.getImageconversion().trim().isEmpty();
                break;
        }

        if (isConfigured) {
            BuildExecution execution = new BuildExecution(buildRequest, productName, buildSystem);
            buildExecutionRepository.save(execution);
        }
    }

    /**
     * Find execution by ID
     */
    public Optional<BuildExecution> findById(Long id) {
        return buildExecutionRepository.findById(id);
    }

    /**
     * Start a build execution
     */
    public BuildExecution startExecution(Long executionId, String buildId, String buildUrl) {
        Optional<BuildExecution> executionOpt = buildExecutionRepository.findById(executionId);
        if (executionOpt.isPresent()) {
            BuildExecution execution = executionOpt.get();
            execution.setStatus(BuildExecution.ExecutionStatus.STARTED);
            execution.setStartedTime(LocalDateTime.now());
            execution.setBuildId(buildId);
            execution.setBuildUrl(buildUrl);
            execution.setUpdatedAt(LocalDateTime.now());
            
            BuildExecution savedExecution = buildExecutionRepository.save(execution);
            
            // Update build request status if this is the first execution to start
            updateBuildRequestStatus(execution.getBuildRequest());
            
            return savedExecution;
        }
        throw new RuntimeException("Build execution not found");
    }

    /**
     * Complete a build execution with success
     */
    public BuildExecution completeExecution(Long executionId) {
        return updateExecutionStatus(executionId, BuildExecution.ExecutionStatus.SUCCESS, null);
    }

    /**
     * Fail a build execution
     */
    public BuildExecution failExecution(Long executionId, String errorMessage) {
        return updateExecutionStatus(executionId, BuildExecution.ExecutionStatus.FAILED, errorMessage);
    }

    /**
     * Update execution status
     */
    public BuildExecution updateExecutionStatus(Long executionId, BuildExecution.ExecutionStatus status, String errorMessage) {
        Optional<BuildExecution> executionOpt = buildExecutionRepository.findById(executionId);
        if (executionOpt.isPresent()) {
            BuildExecution execution = executionOpt.get();
            execution.setStatus(status);
            execution.setCompletedTime(LocalDateTime.now());
            execution.setErrorMessage(errorMessage);
            execution.setUpdatedAt(LocalDateTime.now());
            
            BuildExecution savedExecution = buildExecutionRepository.save(execution);
            
            // Update build request status
            updateBuildRequestStatus(execution.getBuildRequest());
            
            return savedExecution;
        }
        throw new RuntimeException("Build execution not found");
    }

    /**
     * Retry a failed execution
     */
    public BuildExecution retryExecution(Long executionId) {
        Optional<BuildExecution> executionOpt = buildExecutionRepository.findById(executionId);
        if (executionOpt.isPresent()) {
            BuildExecution originalExecution = executionOpt.get();
            
            // Create a new execution for retry
            BuildExecution retryExecution = new BuildExecution(
                originalExecution.getBuildRequest(),
                originalExecution.getProductName(),
                originalExecution.getBuildSystem()
            );
            retryExecution.setRetryCount(originalExecution.getRetryCount() + 1);
            
            return buildExecutionRepository.save(retryExecution);
        }
        throw new RuntimeException("Build execution not found");
    }

    /**
     * Get all executions for a build request
     */
    public List<BuildExecution> getExecutionsByBuildRequest(BuildRequest buildRequest) {
        return buildExecutionRepository.findByBuildRequestOrderByCreatedAtDesc(buildRequest);
    }

    /**
     * Get latest executions for each product
     */
    public List<BuildExecution> getLatestExecutions(BuildRequest buildRequest) {
        return buildExecutionRepository.findLatestExecutionsByBuildRequest(buildRequest);
    }

    /**
     * Check if all builds are ready for automation
     */
    public boolean areAllBuildsSuccessful(BuildRequest buildRequest) {
        return buildExecutionRepository.areAllLatestExecutionsSuccessful(buildRequest);
    }

    /**
     * Get execution summary grouped by build system
     */
    public Map<BuildExecution.BuildSystem, List<BuildExecution>> getExecutionSummary(BuildRequest buildRequest) {
        List<BuildExecution> latestExecutions = getLatestExecutions(buildRequest);
        return latestExecutions.stream()
                .collect(Collectors.groupingBy(BuildExecution::getBuildSystem));
    }

    /**
     * Update build request status based on execution states
     */
    private void updateBuildRequestStatus(BuildRequest buildRequest) {
        List<BuildExecution> allExecutions = getExecutionsByBuildRequest(buildRequest);
        List<BuildExecution> latestExecutions = getLatestExecutions(buildRequest);
        
        if (allExecutions.isEmpty()) {
            return;
        }

        boolean hasStartedExecutions = latestExecutions.stream()
                .anyMatch(e -> e.getStatus() == BuildExecution.ExecutionStatus.STARTED);
        
        boolean allCompleted = latestExecutions.stream()
                .allMatch(BuildExecution::isCompleted);
        
        boolean allSuccessful = latestExecutions.stream()
                .allMatch(BuildExecution::isSuccessful);

        if (hasStartedExecutions && !allCompleted) {
            buildRequest.setStatus(BuildRequest.BuildStatus.BUILDS_IN_PROGRESS);
        } else if (allCompleted && allSuccessful) {
            buildRequest.setStatus(BuildRequest.BuildStatus.BUILDS_COMPLETED);
        } else if (allCompleted && !allSuccessful) {
            buildRequest.setStatus(BuildRequest.BuildStatus.BUILD_TRACKING); // Allow retries
        }

        buildService.updateBuildRequest(buildRequest);
    }

    /**
     * Get failed executions that can be retried
     */
    public List<BuildExecution> getFailedExecutionsForRetry(BuildRequest buildRequest) {
        return buildExecutionRepository.findFailedExecutionsForRetry(buildRequest);
    }
}
