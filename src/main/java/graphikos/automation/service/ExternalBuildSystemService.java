package graphikos.automation.service;

import graphikos.automation.model.BuildExecution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Random;
import java.util.concurrent.CompletableFuture;

/**
 * Service to simulate external build system interactions
 * In a real implementation, this would integrate with actual CMTools and Service Delivery APIs
 */
@Service
@EnableAsync
public class ExternalBuildSystemService {

    @Autowired
    private BuildExecutionService buildExecutionService;

    private final Random random = new Random();

    /**
     * Simulate triggering a build in external system
     * This would be replaced with actual API calls to CMTools/Service Delivery
     */
    @Async
    public CompletableFuture<Void> triggerBuild(Long executionId, String productName, 
                                               BuildExecution.BuildSystem buildSystem) {
        try {
            // Generate mock build ID and URL
            String buildId = generateBuildId(productName, buildSystem);
            String buildUrl = generateBuildUrl(buildId, buildSystem);
            
            // Start the execution
            buildExecutionService.startExecution(executionId, buildId, buildUrl);
            
            // Simulate build duration (2-10 minutes)
            int buildDurationSeconds = 120 + random.nextInt(480); // 2-10 minutes
            Thread.sleep(buildDurationSeconds * 1000);
            
            // Simulate success/failure (80% success rate)
            boolean isSuccess = random.nextDouble() < 0.8;
            
            if (isSuccess) {
                buildExecutionService.completeExecution(executionId);
            } else {
                String errorMessage = generateRandomErrorMessage(productName);
                buildExecutionService.failExecution(executionId, errorMessage);
            }
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            buildExecutionService.failExecution(executionId, "Build interrupted: " + e.getMessage());
        } catch (Exception e) {
            buildExecutionService.failExecution(executionId, "Build failed: " + e.getMessage());
        }
        
        return CompletableFuture.completedFuture(null);
    }

    /**
     * Simulate polling build status from external system
     * This would be replaced with actual API calls
     */
    public BuildStatusResponse pollBuildStatus(String buildId) {
        // In real implementation, this would call external APIs
        // For simulation, we'll return random status
        
        BuildStatusResponse response = new BuildStatusResponse();
        response.setBuildId(buildId);
        response.setStatus(getRandomStatus());
        response.setLastUpdated(LocalDateTime.now());
        
        if (response.getStatus() == BuildExecution.ExecutionStatus.FAILED) {
            response.setErrorMessage(generateRandomErrorMessage("unknown"));
        }
        
        return response;
    }

    /**
     * Start all builds for a build request
     */
    public void startAllBuilds(Long buildRequestId) {
        // This would typically:
        // 1. Get all pending executions for the build request
        // 2. Trigger builds in external systems
        // 3. Start polling for status updates
        
        // For now, we'll implement this in the frontend via manual triggers
    }

    // Helper methods for simulation
    private String generateBuildId(String productName, BuildExecution.BuildSystem buildSystem) {
        String prefix = buildSystem == BuildExecution.BuildSystem.CMTOOLS ? "CM" : "SD";
        return prefix + "_" + productName.toUpperCase() + "_" + System.currentTimeMillis();
    }

    private String generateBuildUrl(String buildId, BuildExecution.BuildSystem buildSystem) {
        String baseUrl = buildSystem == BuildExecution.BuildSystem.CMTOOLS 
            ? "https://cmtools.internal.com/builds/" 
            : "https://sd.internal.com/builds/";
        return baseUrl + buildId;
    }

    private String generateRandomErrorMessage(String productName) {
        String[] errors = {
            "Compilation failed: Missing dependency for " + productName,
            "Unit tests failed: 3 test cases failed",
            "Build timeout: Process exceeded 30 minutes",
            "Configuration error: Invalid build parameters",
            "Network error: Unable to download dependencies",
            "Memory error: Out of heap space during build",
            "Permission error: Access denied to build artifacts"
        };
        return errors[random.nextInt(errors.length)];
    }

    private BuildExecution.ExecutionStatus getRandomStatus() {
        double rand = random.nextDouble();
        if (rand < 0.1) return BuildExecution.ExecutionStatus.PENDING;
        if (rand < 0.3) return BuildExecution.ExecutionStatus.STARTED;
        if (rand < 0.8) return BuildExecution.ExecutionStatus.SUCCESS;
        return BuildExecution.ExecutionStatus.FAILED;
    }

    /**
     * Response class for build status polling
     */
    public static class BuildStatusResponse {
        private String buildId;
        private BuildExecution.ExecutionStatus status;
        private LocalDateTime lastUpdated;
        private String errorMessage;
        private Integer progressPercentage;

        // Getters and setters
        public String getBuildId() { return buildId; }
        public void setBuildId(String buildId) { this.buildId = buildId; }

        public BuildExecution.ExecutionStatus getStatus() { return status; }
        public void setStatus(BuildExecution.ExecutionStatus status) { this.status = status; }

        public LocalDateTime getLastUpdated() { return lastUpdated; }
        public void setLastUpdated(LocalDateTime lastUpdated) { this.lastUpdated = lastUpdated; }

        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }

        public Integer getProgressPercentage() { return progressPercentage; }
        public void setProgressPercentage(Integer progressPercentage) { this.progressPercentage = progressPercentage; }
    }
}
