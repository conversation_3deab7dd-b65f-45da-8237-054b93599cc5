// Main JavaScript functionality for Graphikos Automation

document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu toggle
    const mobileMenuToggle = document.getElementById('mobileMenuToggle');
    const navMenu = document.querySelector('.nav-menu');
    
    if (mobileMenuToggle && navMenu) {
        mobileMenuToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
        });
    }

    // Initialize page-specific functionality
    initializePageFunctionality();
});

function initializePageFunctionality() {
    const currentPath = window.location.pathname;
    
    if (currentPath.includes('nic-checks')) {
        initializeNicChecks();
    } else if (currentPath.includes('update-build')) {
        initializeUpdateBuild();
    } else if (currentPath.includes('check-status')) {
        initializeCheckStatus();
    }
}

// NIC Checks functionality
function initializeNicChecks() {
    const releaseTypeSelect = document.getElementById('releaseType');
    const buildForm = document.getElementById('buildForm');
    const automationConfigForm = document.getElementById('automationConfigForm');
    
    if (releaseTypeSelect) {
        releaseTypeSelect.addEventListener('change', function() {
            if (this.value) {
                buildForm.style.display = 'block';
                showNotification('Release type selected: ' + this.value, 'success');
            } else {
                buildForm.style.display = 'none';
            }
        });
    }

    // Build form submission
    if (buildForm) {
        buildForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitBuildForm();
        });
    }

    // Automation form submission
    if (automationConfigForm) {
        automationConfigForm.addEventListener('submit', function(e) {
            e.preventDefault();
            startAutomation();
        });
    }

    // Initialize checkbox handlers
    initializeCheckboxHandlers();
    
    // Initialize precheck handlers for admin users
    initializePrecheckHandlers();
}

function submitBuildForm() {
    const formData = new FormData(document.getElementById('buildForm'));
    const buildData = Object.fromEntries(formData.entries());
    
    // Add release type
    buildData.releaseType = document.getElementById('releaseType').value;
    
    // Handle checkboxes
    buildData.enableAutobuildUpdate = document.querySelector('input[name="enableAutobuildUpdate"]').checked;
    buildData.conversionAutoBuildUpdate = document.querySelector('input[name="conversionAutoBuildUpdate"]').checked;
    buildData.picturesAutoBuildUpdate = document.querySelector('input[name="picturesAutoBuildUpdate"]').checked;
    buildData.imageconversionAutoBuildUpdate = document.querySelector('input[name="imageconversionAutoBuildUpdate"]').checked;

    fetch('/gqa/api/builds/submit', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(buildData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Build submitted successfully!', 'success');
            updateBuildStatus('Build submitted and processing...');

            // Store build ID for later use
            window.currentBuildId = data.buildId;

            // Initialize build execution tracking
            initializeBuildExecutionTracking(data.buildId);

            // Show build status container
            const buildStatusContainer = document.getElementById('buildStatusContainer');
            if (buildStatusContainer) {
                buildStatusContainer.style.display = 'block';
            }

            // Show automation section (but keep button disabled until builds complete)
            const automationSection = document.getElementById('automationSection');
            if (automationSection) {
                automationSection.style.display = 'block';
            }
            disableAutomationButton();

            // Start dummy build simulation
            startDummyBuildSimulation();
        } else {
            showNotification('Error: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Network error occurred', 'error');
    });
}

function startAutomation() {
    if (!window.currentBuildId) {
        showNotification('Please submit build first', 'warning');
        return;
    }

    const startButton = document.getElementById('startAutomationBtn');
    startButton.disabled = true;
    startButton.textContent = 'Starting Automation...';

    fetch(`/gqa/api/builds/${window.currentBuildId}/start-automation`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Automation started successfully!', 'success');
            
            // Show automation report URL
            const reportSection = document.getElementById('automationReportSection');
            const reportUrl = document.getElementById('automationReportUrl');
            reportUrl.href = data.reportUrl;
            reportUrl.textContent = data.reportUrl;
            reportSection.style.display = 'block';
            
            // Enable report received checkbox
            document.getElementById('reportReceivedCheckbox').disabled = false;
            
            // Show manual testcase section
            document.getElementById('manualTestcaseSection').style.display = 'block';
        } else {
            showNotification('Error: ' + data.message, 'error');
        }
        
        startButton.disabled = false;
        startButton.textContent = 'Start Automation';
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Network error occurred', 'error');
        startButton.disabled = false;
        startButton.textContent = 'Start Automation';
    });
}

function initializeCheckboxHandlers() {
    const reportReceivedCheckbox = document.getElementById('reportReceivedCheckbox');
    const manualTestcaseCheckbox = document.getElementById('manualTestcaseCheckbox');
    
    if (reportReceivedCheckbox) {
        reportReceivedCheckbox.addEventListener('change', function() {
            updateCheckboxStatus('reportReceived', this.checked);
            checkPreBuildUpdateConditions();
        });
    }
    
    if (manualTestcaseCheckbox) {
        manualTestcaseCheckbox.addEventListener('change', function() {
            updateCheckboxStatus('manualTestcaseConfirmed', this.checked);
            checkPreBuildUpdateConditions();
        });
    }

    // Pre-build checkboxes
    const preSanityCheckbox = document.getElementById('preSanityCheckbox');
    const preAutomationCheckbox = document.getElementById('preAutomationCheckbox');
    
    if (preSanityCheckbox) {
        preSanityCheckbox.addEventListener('change', function() {
            updateCheckboxStatus('preSanity', this.checked);
        });
    }
    
    if (preAutomationCheckbox) {
        preAutomationCheckbox.addEventListener('change', function() {
            updateCheckboxStatus('preAutomation', this.checked);
        });
    }

    // Final sanity checkbox
    const finalSanityCheckbox = document.getElementById('finalSanityCheckbox');
    if (finalSanityCheckbox) {
        finalSanityCheckbox.addEventListener('change', function() {
            updateCheckboxStatus('finalSanityCompleted', this.checked);
            if (this.checked) {
                document.getElementById('completeButton').style.display = 'block';
            }
        });
    }
}

function checkPreBuildUpdateConditions() {
    const reportReceived = document.getElementById('reportReceivedCheckbox').checked;
    const manualTestcaseConfirmed = document.getElementById('manualTestcaseCheckbox').checked;
    
    if (reportReceived && manualTestcaseConfirmed) {
        document.getElementById('preBuildUpdateSection').style.display = 'block';
        showNotification('Build is started and once completed automation will be triggered', 'info');
    }
}

function updateBuildInPre() {
    if (!window.currentBuildId) {
        showNotification('No active build found', 'error');
        return;
    }

    fetch(`/gqa/api/builds/${window.currentBuildId}/update-status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: 'PRE_BUILD_UPDATED' })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Pre build updated successfully!', 'success');
            document.getElementById('preCheckboxesSection').style.display = 'block';
            
            // Show pre automation checkbox after report is available
            if (document.getElementById('automationReportUrl').href) {
                document.getElementById('preAutomationCheckbox').parentElement.style.display = 'block';
            }
        } else {
            showNotification('Error: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Network error occurred', 'error');
    });
}

function updateCheckboxStatus(checkboxName, checked) {
    if (!window.currentBuildId) return;

    const data = {};
    data[checkboxName] = checked;

    fetch(`/gqa/api/builds/${window.currentBuildId}/update-checkboxes`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (!data.success) {
            showNotification('Error updating checkbox: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

function initializePrecheckHandlers() {
    const isAdmin = document.body.dataset.userRole === 'ADMIN';
    if (!isAdmin) return;

    const precheckCheckboxes = document.querySelectorAll('.precheck-checkbox input[type="checkbox"]');
    precheckCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updatePrecheckStatus();
            checkLiveBuildUpdateConditions();
        });
    });

    // Precheck textarea handlers
    const precheckTextareas = document.querySelectorAll('.precheck-textarea');
    precheckTextareas.forEach(textarea => {
        textarea.addEventListener('blur', function() {
            savePrecheckContent();
        });
    });
}

function updatePrecheckStatus() {
    if (!window.currentBuildId) return;

    const precheckData = {
        buildDiffChecked: document.getElementById('buildDiffChecked').checked,
        changedFilesChecked: document.getElementById('changedFilesChecked').checked,
        zdcmFilesChecked: document.getElementById('zdcmFilesChecked').checked,
        migrationFilesChecked: document.getElementById('migrationFilesChecked').checked
    };

    fetch(`/gqa/api/builds/${window.currentBuildId}/update-prechecks`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(precheckData)
    })
    .then(response => response.json())
    .then(data => {
        if (!data.success) {
            showNotification('Error updating prechecks: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

function savePrecheckContent() {
    if (!window.currentBuildId) return;

    const contentData = {
        buildDiffContent: document.getElementById('buildDiffContent').value,
        changedFilesContent: document.getElementById('changedFilesContent').value,
        zdcmFilesContent: document.getElementById('zdcmFilesContent').value,
        migrationFilesContent: document.getElementById('migrationFilesContent').value
    };

    fetch(`/gqa/api/builds/${window.currentBuildId}/update-prechecks`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(contentData)
    })
    .catch(error => {
        console.error('Error saving precheck content:', error);
    });
}

function checkLiveBuildUpdateConditions() {
    const allChecked = document.getElementById('buildDiffChecked').checked &&
                      document.getElementById('changedFilesChecked').checked &&
                      document.getElementById('zdcmFilesChecked').checked &&
                      document.getElementById('migrationFilesChecked').checked;

    if (allChecked) {
        document.getElementById('liveBuildUpdateSection').style.display = 'block';
    }
}

function updateBuildToLive() {
    if (!window.currentBuildId) {
        showNotification('No active build found', 'error');
        return;
    }

    const button = document.getElementById('updateToLiveBtn');
    button.disabled = true;
    button.textContent = 'Updating...';

    fetch(`/gqa/api/builds/${window.currentBuildId}/update-status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: 'LIVE_BUILD_UPDATED' })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Live build updated successfully!', 'success');
            updateBuildStatus('Success - Build updated to live');
            document.getElementById('finalSanitySection').style.display = 'block';
        } else {
            showNotification('Error: ' + data.message, 'error');
            updateBuildStatus('Failed - ' + data.message);
        }
        
        button.disabled = false;
        button.textContent = 'Update Build to Live';
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Network error occurred', 'error');
        updateBuildStatus('Failed - Network error');
        button.disabled = false;
        button.textContent = 'Update Build to Live';
    });
}

function completeBuild() {
    if (!window.currentBuildId) {
        showNotification('No active build found', 'error');
        return;
    }

    fetch(`/gqa/api/builds/${window.currentBuildId}/update-status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: 'COMPLETED' })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Build completed successfully!', 'success');
            
            // Get final JSON data
            return fetch(`/gqa/api/builds/${window.currentBuildId}/json`);
        } else {
            throw new Error(data.message);
        }
    })
    .then(response => response.json())
    .then(jsonData => {
        console.log('Final build data:', jsonData);
        showNotification('Build data saved in JSON format', 'success');
        
        // Optionally redirect to status page
        setTimeout(() => {
            window.location.href = '/gqa/check-status';
        }, 2000);
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Error completing build: ' + error.message, 'error');
    });
}

function updateBuildStatus(message) {
    const statusElement = document.getElementById('buildStatus');
    if (statusElement) {
        statusElement.textContent = message;
        statusElement.style.display = 'block';
    }
}

// Update Build page functionality
function initializeUpdateBuild() {
    // Implementation for update build page
    console.log('Update Build page initialized');
}

// Check Status page functionality
function initializeCheckStatus() {
    loadBuildStatusTable();
}

function loadBuildStatusTable() {
    // This would be implemented to load and display the status table
    console.log('Loading build status table');
}

// Build Execution Tracking Functions
let buildExecutionPollingInterval = null;

function initializeBuildExecutionTracking(buildId) {
    currentBuildId = buildId;

    // Load initial execution status
    loadBuildExecutions(buildId);

    // Start polling for status updates every 10 seconds
    if (buildExecutionPollingInterval) {
        clearInterval(buildExecutionPollingInterval);
    }

    buildExecutionPollingInterval = setInterval(() => {
        loadBuildExecutions(buildId);
    }, 10000);
}

function loadBuildExecutions(buildId) {
    fetch(`/gqa/api/build-executions/build/${buildId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateBuildExecutionUI(data);

                // Check if automation can be started - use both old and new systems
                const automationReady = data.allBuildsSuccessful && data.buildRequestStatus === 'BUILDS_COMPLETED';

                if (automationReady) {
                    enableAutomationButton();
                } else {
                    disableAutomationButton();
                }

                // The updateAutomationReadiness is already called in updateBuildExecutionUI
                // but we ensure it's called with the correct status
                updateAutomationReadiness(automationReady);
            }
        })
        .catch(error => {
            console.error('Error loading build executions:', error);
        });
}

function updateBuildExecutionUI(data) {
    const buildStatusContainer = document.getElementById('buildStatusContainer');
    const buildTrackingSection = document.getElementById('buildTrackingSection');

    if (!buildStatusContainer || !buildTrackingSection) return;

    // Ensure the build status container is visible
    buildStatusContainer.style.display = 'block';

    // Update CMTools section
    updateBuildSystemSection('cmtools', data.summary.CMTOOLS || []);

    // Update Service Delivery section
    updateBuildSystemSection('servicedelivery', data.summary.SERVICE_DELIVERY || []);

    // Update progress tracking
    updateBuildProgress(data);

    // Update automation readiness
    updateAutomationReadiness(data.allBuildsSuccessful);
}

function updateBuildProgress(data) {
    const allExecutions = [...(data.summary.CMTOOLS || []), ...(data.summary.SERVICE_DELIVERY || [])];
    const totalBuilds = allExecutions.length;
    const completedBuilds = allExecutions.filter(e => e.status === 'SUCCESS').length;
    const failedBuilds = allExecutions.filter(e => e.status === 'FAILED').length;
    const runningBuilds = allExecutions.filter(e => e.status === 'STARTED').length;
    const pendingBuilds = allExecutions.filter(e => e.status === 'PENDING').length;

    // Update progress bar
    const progressFill = document.getElementById('progressBarFill');
    const progressText = document.getElementById('progressText');
    const overallStatus = document.getElementById('overallProgressStatus');

    if (progressFill && progressText && overallStatus) {
        const progressPercentage = totalBuilds > 0 ? (completedBuilds / totalBuilds) * 100 : 0;

        progressFill.style.width = `${progressPercentage}%`;
        progressFill.className = 'progress-fill';

        if (runningBuilds > 0) {
            progressFill.classList.add('in-progress');
        }

        progressText.textContent = `${completedBuilds} of ${totalBuilds} builds completed`;

        // Update overall status
        if (data.allBuildsSuccessful) {
            overallStatus.innerHTML = '<span class="status-success">✓ All builds completed successfully</span>';
        } else if (failedBuilds > 0) {
            overallStatus.innerHTML = `<span class="status-error">✗ ${failedBuilds} builds failed</span>`;
        } else if (runningBuilds > 0) {
            overallStatus.innerHTML = `<span class="status-warning">⚠ ${runningBuilds} builds running, ${pendingBuilds} pending</span>`;
        } else {
            overallStatus.innerHTML = `<span class="status-warning">⚠ ${pendingBuilds} builds pending</span>`;
        }
    }
}

function updateAutomationReadiness(allBuildsSuccessful) {
    const automationSection = document.getElementById('automationSection');
    const automationBanner = document.getElementById('automationStatusBanner');
    const automationFormContainer = document.getElementById('automationFormContainer');
    const automationReadiness = document.getElementById('automationReadiness');

    if (!automationSection || !automationBanner || !automationFormContainer) return;

    if (allBuildsSuccessful) {
        // Enable automation
        automationSection.className = 'form-section automation-enabled';
        automationBanner.className = 'automation-status-banner ready';
        automationBanner.innerHTML = `
            <div class="banner-content">
                <div class="banner-icon">✅</div>
                <div class="banner-text">
                    <h4>Automation Configuration</h4>
                    <p>All builds completed successfully! You can now configure automation.</p>
                </div>
            </div>
        `;
        automationFormContainer.className = 'automation-form-container';

        if (automationReadiness) {
            automationReadiness.className = 'automation-readiness-indicator ready';
            automationReadiness.innerHTML = '<span class="readiness-text">✅ Automation is now enabled</span>';
        }
    } else {
        // Keep automation disabled
        automationSection.className = 'form-section automation-disabled';
        automationBanner.className = 'automation-status-banner';
        automationBanner.innerHTML = `
            <div class="banner-content">
                <div class="banner-icon">🔒</div>
                <div class="banner-text">
                    <h4>Automation Configuration</h4>
                    <p>Waiting for all builds to complete before enabling automation...</p>
                </div>
            </div>
        `;
        automationFormContainer.className = 'automation-form-container disabled';

        if (automationReadiness) {
            automationReadiness.className = 'automation-readiness-indicator';
            automationReadiness.innerHTML = '<span class="readiness-text">Automation will be enabled when all builds complete</span>';
        }
    }
}

function updateBuildSystemSection(systemId, executions) {
    const container = document.getElementById(`${systemId}Executions`);
    if (!container) return;

    container.innerHTML = '';

    if (executions.length === 0) {
        container.innerHTML = '<div class="empty-executions">No builds configured for this system</div>';
        return;
    }

    executions.forEach(execution => {
        const executionDiv = document.createElement('div');
        executionDiv.className = `execution-item status-${execution.status.toLowerCase()}`;

        const statusIcon = getStatusIcon(execution.status);
        const timeInfo = getTimeInfo(execution);

        executionDiv.innerHTML = `
            <div class="execution-header">
                <span class="product-name">${execution.productName}</span>
                <span class="status-badge ${execution.status.toLowerCase()}">${statusIcon} ${execution.status}</span>
            </div>
            <div class="execution-details">
                ${timeInfo}
                ${execution.errorMessage ? `<div class="error-message">Error: ${execution.errorMessage}</div>` : ''}
                ${execution.buildUrl ? `<a href="${execution.buildUrl}" target="_blank" class="build-link">View Build</a>` : ''}
            </div>
            <div class="execution-actions">
                ${execution.status === 'PENDING' ? `<button onclick="triggerBuild(${execution.id})" class="btn-trigger">Start Build</button>` : ''}
                ${execution.status === 'FAILED' ? `<button onclick="retryBuild(${execution.id})" class="btn-retry">Retry</button>` : ''}
            </div>
        `;

        container.appendChild(executionDiv);
    });
}

function getStatusIcon(status) {
    switch (status) {
        case 'SUCCESS': return '✓';
        case 'FAILED': return '✗';
        case 'STARTED': return '⟳';
        case 'PENDING': return '⏳';
        default: return '?';
    }
}

function getTimeInfo(execution) {
    const startTime = execution.startedTime ? new Date(execution.startedTime).toLocaleTimeString() : null;
    const endTime = execution.completedTime ? new Date(execution.completedTime).toLocaleTimeString() : null;
    const duration = execution.durationMinutes || 0;

    return `
        <div class="execution-times">
            <div class="time-item">
                <div class="time-label">Started</div>
                <div class="time-value ${!startTime ? 'not-started' : ''}">${startTime || 'Not started'}</div>
            </div>
            <div class="time-item">
                <div class="time-label">Ended</div>
                <div class="time-value ${!endTime ? 'not-started' : ''}">${endTime || 'In progress'}</div>
            </div>
            <div class="time-item">
                <div class="time-label">Duration</div>
                <div class="time-value">
                    ${duration > 0 ? `${duration}m` : 'N/A'}
                    ${duration > 0 ? `<span class="duration-badge">${formatDuration(duration)}</span>` : ''}
                </div>
            </div>
        </div>
    `;
}

function formatDuration(minutes) {
    if (minutes < 60) {
        return `${minutes}m`;
    } else {
        const hours = Math.floor(minutes / 60);
        const remainingMinutes = minutes % 60;
        return `${hours}h ${remainingMinutes}m`;
    }
}

function triggerBuild(executionId) {
    fetch(`/gqa/api/build-executions/${executionId}/trigger`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Build triggered successfully', 'success');
            loadBuildExecutions(currentBuildId);
        } else {
            showNotification('Failed to trigger build: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error triggering build:', error);
        showNotification('Error triggering build', 'error');
    });
}

function retryBuild(executionId) {
    fetch(`/gqa/api/build-executions/${executionId}/retry`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Build retry created successfully', 'success');
            loadBuildExecutions(currentBuildId);
        } else {
            showNotification('Failed to retry build: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error retrying build:', error);
        showNotification('Error retrying build', 'error');
    });
}

function enableAutomationButton() {
    const automationButton = document.getElementById('startAutomationBtn');
    if (automationButton) {
        automationButton.disabled = false;
        automationButton.textContent = 'Start Automation';
        automationButton.classList.remove('disabled');
    }
}

function disableAutomationButton() {
    const automationButton = document.getElementById('startAutomationBtn');
    if (automationButton) {
        automationButton.disabled = true;
        automationButton.textContent = 'Waiting for Builds to Complete';
        automationButton.classList.add('disabled');
    }
}

// Dummy Build Simulation Functions
function startDummyBuildSimulation() {
    if (!window.dummyBuildData) {
        console.error('Dummy build data not initialized');
        return;
    }

    // Reset all builds to initial state
    resetDummyBuildData();

    console.log('🚀 Starting dummy build simulation...');
    showNotification('Starting build simulation...', 'info');

    // Show the build status container
    const buildStatusContainer = document.getElementById('buildStatusContainer');
    if (buildStatusContainer) {
        buildStatusContainer.style.display = 'block';
    }

    // Show automation section but keep it disabled
    const automationSection = document.getElementById('automationSection');
    if (automationSection) {
        automationSection.style.display = 'block';
    }

    // Update UI to show initial state
    updateDummyBuildUI();

    // Start CMTools builds first
    setTimeout(() => {
        startCMToolsBuilds();
    }, 1000);
}

function resetDummyBuildData() {
    if (!window.dummyBuildData) return;

    // Reset CMTools products
    window.dummyBuildData.cmtoolsProducts.forEach(product => {
        product.status = 'PENDING';
        product.startTime = null;
        product.endTime = null;
        product.duration = 0;
    });

    // Reset Service Delivery products
    window.dummyBuildData.serviceDeliveryProducts.forEach(product => {
        product.status = 'PENDING';
        product.startTime = null;
        product.endTime = null;
        product.duration = 0;
    });

    // Reset flags
    window.dummyBuildData.allBuildsSuccessful = false;
    window.dummyBuildData.cmtoolsCompleted = false;
    window.dummyBuildData.serviceDeliveryStarted = false;
}

function startCMToolsBuilds() {
    const cmtoolsProducts = window.dummyBuildData.cmtoolsProducts;
    let currentIndex = 0;

    function buildNextProduct() {
        if (currentIndex >= cmtoolsProducts.length) {
            // All CMTools builds completed
            window.dummyBuildData.cmtoolsCompleted = true;
            console.log('✅ All CMTools builds completed, starting Service Delivery builds...');
            startServiceDeliveryBuilds();
            return;
        }

        const product = cmtoolsProducts[currentIndex];
        console.log(`🔨 Starting build for ${product.name}...`);

        // Start the build
        product.status = 'STARTED';
        product.startTime = new Date();
        updateDummyBuildUI();

        // Simulate build duration (random between 30-120 seconds)
        const buildDuration = Math.random() * (120000 - 30000) + 30000;

        setTimeout(() => {
            // Randomly decide if build succeeds or fails (90% success rate)
            const isSuccess = Math.random() > 0.1;

            if (isSuccess) {
                product.status = 'SUCCESS';
                product.endTime = new Date();
                product.duration = Math.round((product.endTime - product.startTime) / 1000 / 60 * 10) / 10; // minutes with 1 decimal

                console.log(`✅ ${product.name} build completed successfully in ${product.duration} minutes`);
                showNotification(`${product.name} build completed successfully!`, 'success');
                updateDummyBuildUI();

                currentIndex++;
                // Start next build after a short delay
                setTimeout(buildNextProduct, 2000);
            } else {
                product.status = 'FAILED';
                product.endTime = new Date();
                product.duration = Math.round((product.endTime - product.startTime) / 1000 / 60 * 10) / 10;

                console.log(`❌ ${product.name} build failed after ${product.duration} minutes`);
                showNotification(`${product.name} build failed! Check logs for details.`, 'error');
                updateDummyBuildUI();

                // For demo purposes, retry after 5 seconds
                setTimeout(() => {
                    console.log(`🔄 Retrying ${product.name} build...`);
                    showNotification(`Retrying ${product.name} build...`, 'info');
                    product.status = 'STARTED';
                    product.startTime = new Date();
                    updateDummyBuildUI();

                    // Retry with shorter duration and guaranteed success
                    setTimeout(() => {
                        product.status = 'SUCCESS';
                        product.endTime = new Date();
                        product.duration = Math.round((product.endTime - product.startTime) / 1000 / 60 * 10) / 10;

                        console.log(`✅ ${product.name} build completed successfully on retry in ${product.duration} minutes`);
                        showNotification(`${product.name} build completed successfully on retry!`, 'success');
                        updateDummyBuildUI();

                        currentIndex++;
                        setTimeout(buildNextProduct, 2000);
                    }, Math.random() * 20000 + 10000); // 10-30 seconds for retry
                }, 5000);
            }
        }, buildDuration);
    }

    buildNextProduct();
}

function startServiceDeliveryBuilds() {
    window.dummyBuildData.serviceDeliveryStarted = true;
    const serviceDeliveryProducts = window.dummyBuildData.serviceDeliveryProducts;
    let currentIndex = 0;

    function buildNextProduct() {
        if (currentIndex >= serviceDeliveryProducts.length) {
            // All Service Delivery builds completed
            window.dummyBuildData.allBuildsSuccessful = true;
            console.log('🎉 All builds completed successfully!');
            showNotification('🎉 All builds completed successfully! Automation is now enabled.', 'success');
            updateDummyBuildUI();

            // Show completion celebration
            setTimeout(() => {
                showBuildCompletionCelebration();
            }, 1000);
            return;
        }

        const product = serviceDeliveryProducts[currentIndex];
        console.log(`🔨 Starting Service Delivery build for ${product.name}...`);

        // Start the build
        product.status = 'STARTED';
        product.startTime = new Date();
        updateDummyBuildUI();

        // Simulate build duration (random between 20-90 seconds)
        const buildDuration = Math.random() * (90000 - 20000) + 20000;

        setTimeout(() => {
            // Service Delivery builds have higher success rate (95%)
            const isSuccess = Math.random() > 0.05;

            if (isSuccess) {
                product.status = 'SUCCESS';
                product.endTime = new Date();
                product.duration = Math.round((product.endTime - product.startTime) / 1000 / 60 * 10) / 10; // minutes with 1 decimal

                console.log(`✅ ${product.name} Service Delivery build completed successfully in ${product.duration} minutes`);
                showNotification(`Service Delivery: ${product.name} completed successfully!`, 'success');
                updateDummyBuildUI();

                currentIndex++;
                // Start next build after a short delay
                setTimeout(buildNextProduct, 1500);
            } else {
                product.status = 'FAILED';
                product.endTime = new Date();
                product.duration = Math.round((product.endTime - product.startTime) / 1000 / 60 * 10) / 10;

                console.log(`❌ ${product.name} Service Delivery build failed after ${product.duration} minutes`);
                showNotification(`Service Delivery: ${product.name} build failed!`, 'error');
                updateDummyBuildUI();

                // Auto-retry for Service Delivery
                setTimeout(() => {
                    console.log(`🔄 Retrying ${product.name} Service Delivery build...`);
                    showNotification(`Retrying Service Delivery: ${product.name}...`, 'info');
                    product.status = 'STARTED';
                    product.startTime = new Date();
                    updateDummyBuildUI();

                    // Retry with guaranteed success
                    setTimeout(() => {
                        product.status = 'SUCCESS';
                        product.endTime = new Date();
                        product.duration = Math.round((product.endTime - product.startTime) / 1000 / 60 * 10) / 10;

                        console.log(`✅ ${product.name} Service Delivery build completed successfully on retry`);
                        showNotification(`Service Delivery: ${product.name} completed on retry!`, 'success');
                        updateDummyBuildUI();

                        currentIndex++;
                        setTimeout(buildNextProduct, 1500);
                    }, Math.random() * 15000 + 8000); // 8-23 seconds for retry
                }, 3000);
            }
        }, buildDuration);
    }

    buildNextProduct();
}

function updateDummyBuildUI() {
    if (!window.dummyBuildData) return;

    // Create dummy data structure that matches the expected format
    const dummyData = {
        success: true,
        allBuildsSuccessful: window.dummyBuildData.allBuildsSuccessful,
        buildRequestStatus: window.dummyBuildData.allBuildsSuccessful ? 'BUILDS_COMPLETED' : 'IN_PROGRESS',
        summary: {
            CMTOOLS: window.dummyBuildData.cmtoolsProducts.map(product => ({
                id: `cmtools_${product.name.toLowerCase()}`,
                productName: product.name,
                status: product.status,
                startedTime: product.startTime ? product.startTime.toISOString() : null,
                completedTime: product.endTime ? product.endTime.toISOString() : null,
                durationMinutes: product.duration,
                errorMessage: product.status === 'FAILED' ? 'Build failed due to compilation error' : null,
                buildUrl: product.status !== 'PENDING' ? `https://build.example.com/cmtools/${product.name.toLowerCase()}` : null
            })),
            SERVICE_DELIVERY: window.dummyBuildData.serviceDeliveryProducts.map(product => ({
                id: `sd_${product.name.toLowerCase()}`,
                productName: product.name,
                status: window.dummyBuildData.serviceDeliveryStarted ? product.status : 'PENDING',
                startedTime: product.startTime ? product.startTime.toISOString() : null,
                completedTime: product.endTime ? product.endTime.toISOString() : null,
                durationMinutes: product.duration,
                errorMessage: product.status === 'FAILED' ? 'Service deployment failed' : null,
                buildUrl: product.status !== 'PENDING' ? `https://build.example.com/servicedelivery/${product.name.toLowerCase()}` : null
            }))
        }
    };

    // Update the UI using existing function
    updateBuildExecutionUI(dummyData);
}

function showBuildCompletionCelebration() {
    // Add some visual celebration
    const progressBar = document.getElementById('progressBarFill');
    if (progressBar) {
        progressBar.style.background = 'linear-gradient(90deg, #28a745 0%, #20c997 100%)';
        progressBar.style.boxShadow = '0 0 20px rgba(40, 167, 69, 0.5)';
    }

    // Update the overall status with celebration
    const overallStatus = document.getElementById('overallProgressStatus');
    if (overallStatus) {
        overallStatus.innerHTML = '<span class="status-success">🎉 All builds completed successfully! Automation enabled!</span>';
    }

    console.log('🎊 Build completion celebration displayed!');
}

// Make functions globally available
window.startDummyBuildSimulation = startDummyBuildSimulation;
window.resetDummyBuildData = resetDummyBuildData;
window.updateDummyBuildUI = updateDummyBuildUI;

// Utility functions
function updateBuildStatus(message) {
    const statusContent = document.querySelector('.status-content');
    if (statusContent) {
        statusContent.textContent = message;
        statusContent.parentElement.style.display = 'block';
    }
}
