<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Graphikos Automation - QA Team Portal</title>
    <link rel="stylesheet" th:href="@{/css/main.css}">
    <link rel="stylesheet" th:href="@{/css/responsive.css}">
    <link rel="icon" type="image/svg+xml" th:href="@{/images/logo.svg}">
</head>
<body th:data-user-role="${currentUser != null ? currentUser.role.name() : 'GUEST'}">
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <img th:src="@{/images/logo.svg}" alt="Graphikos Automation" class="logo-img">
                </div>
                
                <nav class="nav-menu" sec:authorize="isAuthenticated()">
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="#" class="nav-link active" data-section="nic-checks">NIC Checks</a>
                        </li>
                        <li class="nav-item" th:if="${currentUser != null and currentUser.role.name() == 'ULTIMATE'}">
                            <a href="#" class="nav-link" data-section="update-build">Update Build</a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link" data-section="check-status">Check Status</a>
                        </li>
                        <li class="nav-item" th:if="${currentUser != null and currentUser.role.name() == 'ULTIMATE'}">
                            <a href="#" class="nav-link" data-section="dashboard">Dashboard</a>
                        </li>
                    </ul>
                </nav>
                
                <div class="user-menu" sec:authorize="isAuthenticated()">
                    <div class="user-info">
                        <span class="user-name" th:text="${currentUser != null ? currentUser.fullName : 'User'}">User</span>
                        <span class="user-role" th:text="${currentUser != null ? currentUser.role : 'EDITOR'}">EDITOR</span>
                    </div>
                    <div class="user-actions">
                        <button class="settings-btn" onclick="window.open('/gqa/admin', '_blank')" th:if="${currentUser != null and (currentUser.role.name() == 'ADMIN' or currentUser.role.name() == 'ULTIMATE')}" title="Admin Settings">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="3"></circle>
                                <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                            </svg>
                        </button>
                        <a th:href="@{/logout}" class="logout-btn">Logout</a>
                    </div>
                </div>
                
                <!-- Mobile menu toggle -->
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Notifications -->
            <div class="notifications" id="notifications"></div>
            
            <!-- Dynamic Content Container -->
            <div class="content-wrapper" id="mainContent">
                <!-- Dashboard Section -->
                <div id="dashboard-section" class="content-section">
                    <div class="page-header">
                        <h1 class="page-title">Dashboard</h1>
                        <p class="page-subtitle">QA Team Overview</p>
                    </div>
                    <!-- Dashboard content will be loaded here -->
                </div>

                <!-- NIC Checks Section (Default) -->
                <div id="nic-checks-section" class="content-section active">
                    <div style="padding: 20px; background: #e3f2fd; margin: 20px; border-radius: 8px; border: 1px solid #2196f3;">
                        <h2 style="color: #1976d2; margin: 0 0 10px 0;">🚀 GATE Application</h2>
                        <p style="margin: 0; color: #424242;">Loading NIC Checks content...</p>
                        <div style="margin-top: 10px; font-size: 12px; color: #666;">
                            If this message persists, please check the browser console for errors.
                        </div>
                    </div>
                    <!-- NIC Checks content will be loaded here -->
                </div>

                <!-- Update Build Section -->
                <div id="update-build-section" class="content-section">
                    <div class="page-header">
                        <h1 class="page-title">Update Build</h1>
                        <p class="page-subtitle">Manage Your Build Requests</p>
                    </div>

                    <!-- Build Status Container -->
                    <div id="buildStatusContainer" class="build-status-container" style="display: none;">
                        <!-- Build Execution Tracking Section -->
                        <div id="buildTrackingSection" class="build-tracking-section">
                            <h3 class="section-title">Build Execution Status</h3>

                            <!-- Overall Progress Indicator -->
                            <div class="build-progress-container">
                                <div class="progress-header">
                                    <h4>Overall Build Progress</h4>
                                    <div id="overallProgressStatus" class="progress-status">
                                        <span class="status-warning">⚠ Initializing build tracking...</span>
                                    </div>
                                </div>
                                <div class="progress-bar-container">
                                    <div class="progress-bar">
                                        <div id="progressBarFill" class="progress-fill" style="width: 0%"></div>
                                    </div>
                                    <div class="progress-text">
                                        <span id="progressText">0 of 0 builds completed</span>
                                    </div>
                                </div>
                            </div>

                            <div class="build-system-section">
                                <h4 class="build-system-title">CMTools Builds</h4>
                                <div id="cmtoolsExecutions" class="executions-container">
                                    <!-- CMTools executions will be populated here -->
                                </div>
                            </div>

                            <div class="build-system-section">
                                <h4 class="build-system-title">Service Delivery Builds</h4>
                                <div id="servicedeliveryExecutions" class="executions-container">
                                    <!-- Service Delivery executions will be populated here -->
                                </div>
                            </div>

                            <div class="build-tracking-actions">
                                <button id="refreshBuildStatus" class="btn btn-secondary btn-sm" onclick="loadBuildExecutions(currentBuildId)">
                                    Refresh Status
                                </button>
                                <button id="startDummySimulation" class="btn btn-primary btn-sm" onclick="startDummyBuildSimulation()" style="margin-left: 10px;">
                                    Start Build Simulation
                                </button>
                                <button id="resetSimulation" class="btn btn-warning btn-sm" onclick="resetDummyBuildData(); updateDummyBuildUI();" style="margin-left: 5px;">
                                    Reset Simulation
                                </button>
                                <div class="automation-readiness-indicator" id="automationReadiness">
                                    <span class="readiness-text">Automation will be enabled when all builds complete</span>
                                </div>
                            </div>
                        </div>

                        <!-- Automation Configuration Section -->
                        <div id="automationSection" class="form-section automation-disabled">
                            <div class="automation-status-banner" id="automationStatusBanner">
                                <div class="banner-content">
                                    <div class="banner-icon">🔒</div>
                                    <div class="banner-text">
                                        <h4>Automation Configuration</h4>
                                        <p>Waiting for all builds to complete before enabling automation...</p>
                                    </div>
                                </div>
                            </div>
                            <div id="automationFormContainer" class="automation-form-container disabled">
                                <!-- Automation form will be loaded here -->
                            </div>
                        </div>
                    </div>

                    <!-- Update Build content will be loaded here -->
                </div>

                <!-- Check Status Section -->
                <div id="check-status-section" class="content-section">
                    <div class="page-header">
                        <h1 class="page-title">Build Summaries</h1>
<!--                        <p class="page-subtitle">Build Summaries and JSON Data</p>-->
                    </div>
                    <!-- Check Status content will be loaded here -->
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <p>&copy; 2024 Graphikos Automation - Internal QA Team Tool</p>
                <p>Version 1.0.0</p>
            </div>
        </div>
    </footer>



    <!-- JavaScript -->
    <script th:src="@{/js/main.js}"></script>
    <script th:src="@{/js/notifications.js}"></script>
    <script th:src="@{/js/spa.js}"></script>
</body>
</html>
